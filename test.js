"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var node_pty_1 = require("node-pty");
var os_1 = require("os");
var shell = os_1.default.platform() === "win32" ? "powershell.exe" : "bash";
var ptyProcess = node_pty_1.default.spawn("claude", ["agent"], {
    name: "xterm-color",
    cols: 80,
    rows: 30,
    cwd: process.cwd(),
    env: process.env,
});
ptyProcess.onData(function (data) {
    process.stdout.write(data);
    console.log(data);
});
