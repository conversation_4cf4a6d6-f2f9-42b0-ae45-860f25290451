/**
 * HTML分析器 (Electron渲染进程版本)
 * 用于分析HTML文件中的DOM结构和样式
 * 通过IPC与主进程通信来处理文件系统操作
 */

/**
 * 分析HTML文件中未定义样式的类名，并获取对应的Figma节点信息
 * @param folderPath 文件夹路径，如 /Users/<USER>/xunshan/xxx/
 * @returns Promise<any> 返回分析结果，包含Figma节点信息
 */
export async function analyzeHtmlAndGetFigmaInfo(folderPath: string): Promise<any> {
  try {
    const result = await window.ipcRenderer.invoke('analyze-html-folder', {
      folderPath
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    return result.data;

  } catch (error) {
    console.error('❌ 分析HTML并获取Figma信息失败:', error);
    throw error;
  }
}

/**
 * 带有自定义Figma URL的分析函数
 * @param folderPath 文件夹路径
 * @param baseFigmaUrl 基础Figma URL
 * @returns Promise<any> 返回分析结果，包含Figma节点信息
 */
export async function analyzeHtmlAndGetFigmaInfoWithUrl(
  folderPath: string,
  baseFigmaUrl: string
): Promise<any> {
  try {
    const result = await window.ipcRenderer.invoke('analyze-html-folder', {
      folderPath,
      baseFigmaUrl
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    return result.data;

  } catch (error) {
    console.error('❌ 分析HTML并获取Figma信息失败:', error);
    throw error;
  }
}

export default {
  analyzeHtmlAndGetFigmaInfo,
  analyzeHtmlAndGetFigmaInfoWithUrl
};
