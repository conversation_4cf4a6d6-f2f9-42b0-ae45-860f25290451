/**
 * Claude CLI 封装类 (TypeScript版本)
 * 用于管理和控制 Claude CLI 进程
 */

import * as pty from 'node-pty';

export interface ClaudeCLIOptions {
  proxy?: string;
  idleTimeoutMs?: number;
  onReady?: () => void;
  onData?: (data: string) => void;
}

export class ClaudeCLI {
  private proxy: string;
  private idleTimeoutMs: number;
  private ready: boolean;
  private idleTimer: NodeJS.Timeout | null;
  private ptyProcess: pty.IPty | null;
  private onReadyCallback: (() => void) | null;
  private onDataCallback: ((data: string) => void) | null;

  constructor(options: ClaudeCLIOptions = {}) {
    this.proxy = options.proxy || "http://127.0.0.1:7897";
    this.idleTimeoutMs = options.idleTimeoutMs || 2000; // 连续 2 秒无输出
    this.ready = false;
    this.idleTimer = null;
    this.ptyProcess = null;
    this.onReadyCallback = options.onReady || null;
    this.onDataCallback = options.onData || null;

    // 初始化 Claude CLI 实例
    this._initializeCLI();
  }

  private _initializeCLI(): void {
    const env = {
      ...process.env,
      http_proxy: this.proxy,
      https_proxy: this.proxy,
    };

    this.ptyProcess = pty.spawn("claude", ["agent"], {
      name: "xterm-color",
      cols: 100,
      rows: 30,
      cwd: process.cwd(),
      env,
    });

    this.ready = false;
    this._resetIdleTimer();

    // 输出监听
    this.ptyProcess.onData((data: string) => {
      if (this.onDataCallback) {
        this.onDataCallback(data);
      } else {
        process.stdout.write(data);
      }
      this._resetIdleTimer();
    });

    // 监听进程退出
    this.ptyProcess.onExit((exitCode: number, signal?: number) => {
      console.log(`\n⚠️ Claude CLI 进程退出，退出码: ${exitCode}, 信号: ${signal}`);
      this._clearIdleTimer();
    });
  }

  private _resetIdleTimer(): void {
    this._clearIdleTimer();
    this.idleTimer = setTimeout(() => {
      if (!this.ready) {
        this.ready = true;
        console.log("\n✅ 检测到 2s 无输出，Claude Agent 已 ready");
        if (this.onReadyCallback) {
          this.onReadyCallback();
        }
      }
    }, this.idleTimeoutMs);
  }

  private _clearIdleTimer(): void {
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
      this.idleTimer = null;
    }
  }

  public send(prompt: string): void {
    if (!this.ptyProcess) {
      throw new Error("Claude CLI 实例未初始化");
    }

    // 清空当前输入行（Ctrl+U）
    this.ptyProcess.write("\u0015");

    // 输入文本
    this.ptyProcess.write(prompt);

    // 模拟延迟后按下回车（更稳妥）
    setTimeout(() => {
      if (this.ptyProcess) {
        this.ptyProcess.write("\r");
      }
    }, 300);
  }

  public reset(): void {
    console.log("🔄 重置 Claude CLI 实例...");

    // 清理当前实例
    this._clearIdleTimer();
    if (this.ptyProcess) {
      this.ptyProcess.kill();
      this.ptyProcess = null;
    }

    // 重新初始化
    this._initializeCLI();
    console.log("✅ Claude CLI 实例已重置");
  }

  public isReady(): boolean {
    return this.ready;
  }

  public destroy(): void {
    this._clearIdleTimer();
    if (this.ptyProcess) {
      this.ptyProcess.kill();
      this.ptyProcess = null;
    }
  }
}

export default ClaudeCLI;
