import pty from "node-pty";

class ClaudeCLI {
  constructor(options = {}) {
    this.proxy = options.proxy || "http://127.0.0.1:7897";
    this.idleTimeoutMs = options.idleTimeoutMs || 2000; // 连续 2 秒无输出
    this.ready = false;
    this.idleTimer = null;
    this.ptyProcess = null;
    this.onReadyCallback = options.onReady || null;
    this.onDataCallback = options.onData || null;

    // 初始化 Claude CLI 实例
    this._initializeCLI();
  }

  _initializeCLI() {
    const env = {
      ...process.env,
      http_proxy: this.proxy,
      https_proxy: this.proxy,
    };

    this.ptyProcess = pty.spawn("claude", ["agent"], {
      name: "xterm-color",
      cols: 100,
      rows: 30,
      cwd: process.cwd(),
      env,
    });

    this.ready = false;
    this._resetIdleTimer();

    // 输出监听
    this.ptyProcess.onData((data) => {
      if (this.onDataCallback) {
        this.onDataCallback(data);
      } else {
        process.stdout.write(data);
      }
      this._resetIdleTimer();
    });

    // 监听进程退出
    this.ptyProcess.onExit((exitCode, signal) => {
      console.log(`\n⚠️ Claude CLI 进程退出，退出码: ${exitCode}, 信号: ${signal}`);
      this._clearIdleTimer();
    });
  }

  _resetIdleTimer() {
    this._clearIdleTimer();
    this.idleTimer = setTimeout(() => {
      if (!this.ready) {
        this.ready = true;
        console.log("\n✅ 检测到 2s 无输出，Claude Agent 已 ready");
        if (this.onReadyCallback) {
          this.onReadyCallback();
        }
      }
    }, this.idleTimeoutMs);
  }

  _clearIdleTimer() {
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
      this.idleTimer = null;
    }
  }

  send(prompt) {
    if (!this.ptyProcess) {
      throw new Error("Claude CLI 实例未初始化");
    }

    // 清空当前输入行（Ctrl+U）
    this.ptyProcess.write("\u0015");

    // 输入文本
    this.ptyProcess.write(prompt);

    // 模拟延迟后按下回车（更稳妥）
    setTimeout(() => {
      this.ptyProcess.write("\r");
    }, 300);
  }

  reset() {
    console.log("🔄 重置 Claude CLI 实例...");

    // 清理当前实例
    this._clearIdleTimer();
    if (this.ptyProcess) {
      this.ptyProcess.kill();
      this.ptyProcess = null;
    }

    // 重新初始化
    this._initializeCLI();
    console.log("✅ Claude CLI 实例已重置");
  }

  isReady() {
    return this.ready;
  }

  destroy() {
    this._clearIdleTimer();
    if (this.ptyProcess) {
      this.ptyProcess.kill();
      this.ptyProcess = null;
    }
  }
}

// 使用示例
const claude = new ClaudeCLI({
  proxy: "http://127.0.0.1:7897",
  onReady: () => {
    console.log("Claude CLI 准备就绪，可以发送消息了！");
    // 自动发送初始提示
    claude.send("请优化 utils 文件夹中的所有函数为 async/await 风格，并检查是否可以合并重复逻辑。");
  }
});

// 导出类供其他模块使用
export default ClaudeCLI;
