1. 不用考虑设备的兼容性
2. UI不用考虑过多的兼容性，允许粗糙一些。因为是客户自己用，只要过得去就行。这一点上面就非常省时间了。
3. 方案的确定性，以跑通目标为准。什么意思呢，就是客户主要以目标为准，我们可以酌情选择技术方案。这点来说确实是比较好的一点，不用为了很多细节去扯皮，只要能达到客户需要的效果就可以，客户的配合程度比较高，以我们为主导。

4. 写代码其实是比较辅助的一方面，只是为了有个操作界面
5. token消耗预估，需要给客户一个大概的量级。这样能大概评估成本，越复杂的方案，效果越好，但是每次运行的成本也就越高。需要跟客户商量取一个平衡

6. 为什么不直接搭工作流？
第一是因为对程序员来说，工作流不是最快的方式。
第二是因为客户用着不爽，你不可能上生产了让用户取Dify点一点。有个自己的操作界面能省很多事情
第三是因为MCP大多是远程服务，我们需要本地的控制，比如控制代码生成比如控制其他软件，可能你会说用ngrok暴露本地端口到公网这种曲线救国的方式，但是生产环境其实不合适。一个是安全问题还一个是增加了复杂度。

7. 签了保密协议，所以具体逻辑不能讲太多。但是UI的没有保密协议，也没什么值钱的，所以可以给大家演示。如果大家需要架构相似的流程或者解决相近的场景，可以参考参考

8. 你会发现它不是一个单一的工具，是一个完整的解决方案。有很多定制化的地方，（因为要跟用户的团队去沟通，然后需要匹配他们的生产流程，比如代码风格，比如上线流程）可能不适合规模化使用，但是可以解决特定用户的特定的场景，这就是它的价值所在。

9. 需要了解不同大模型的特性，以及优缺点，特别是局限性。这点非常重要，比如国外的大模型最中文支持不友好，尤其是在图片生成的时候，中文经常出现乱码。


最新的咨询，比如你创业有个idea之前，可以放出来讨论讨论，或许有竞品了，或者大家能给点好的建议