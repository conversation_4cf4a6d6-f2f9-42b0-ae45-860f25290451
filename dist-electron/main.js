"use strict";
const electron = require("electron");
const node_module = require("node:module");
const node_url = require("node:url");
const path$1 = require("node:path");
const fs = require("fs");
const path = require("path");
const https = require("https");
const http = require("http");
const querystring = require("querystring");
var _documentCurrentScript = typeof document !== "undefined" ? document.currentScript : null;
function _interopNamespaceDefault(e) {
  const n = Object.create(null, { [Symbol.toStringTag]: { value: "Module" } });
  if (e) {
    for (const k in e) {
      if (k !== "default") {
        const d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: () => e[k]
        });
      }
    }
  }
  n.default = e;
  return Object.freeze(n);
}
const fs__namespace = /* @__PURE__ */ _interopNamespaceDefault(fs);
const path__namespace = /* @__PURE__ */ _interopNamespaceDefault(path);
const https__namespace = /* @__PURE__ */ _interopNamespaceDefault(https);
const http__namespace = /* @__PURE__ */ _interopNamespaceDefault(http);
const querystring__namespace = /* @__PURE__ */ _interopNamespaceDefault(querystring);
function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      const parsedUrl = new URL(url);
      const httpModule = parsedUrl.protocol === "https:" ? https__namespace : http__namespace;
      if (options.params) {
        const queryString = querystring__namespace.stringify(options.params);
        if (queryString) {
          parsedUrl.search = parsedUrl.search ? `${parsedUrl.search}&${queryString}` : `?${queryString}`;
        }
      }
      const requestOptions = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (parsedUrl.protocol === "https:" ? 443 : 80),
        path: parsedUrl.pathname + parsedUrl.search,
        method: options.method || "GET",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "Electron-App/1.0.0",
          ...options.headers
        },
        timeout: options.timeout || 1e4
      };
      const req = httpModule.request(requestOptions, (res) => {
        const chunks = [];
        res.on("data", (chunk) => {
          chunks.push(chunk);
        });
        res.on("end", () => {
          try {
            const statusCode = res.statusCode || 0;
            if (statusCode >= 400) {
              reject(new Error(`HTTP Error: ${statusCode} ${res.statusMessage}`));
              return;
            }
            const responseBuffer = Buffer.concat(chunks);
            let parsedData;
            if (options.responseType === "buffer") {
              parsedData = responseBuffer;
            } else {
              const responseData = responseBuffer.toString("utf8");
              const contentType = res.headers["content-type"] || "";
              if (options.responseType === "json" || contentType.includes("application/json") && responseData.trim()) {
                try {
                  parsedData = JSON.parse(responseData);
                } catch (parseError) {
                  parsedData = responseData;
                }
              } else {
                parsedData = responseData;
              }
            }
            resolve({
              data: parsedData,
              status: statusCode,
              statusText: res.statusMessage || "OK",
              headers: res.headers
            });
          } catch (error) {
            reject(new Error(`Response parsing error: ${error instanceof Error ? error.message : "Unknown error"}`));
          }
        });
      });
      req.on("error", (error) => {
        reject(new Error(`Network error: ${error.message}`));
      });
      req.on("timeout", () => {
        req.destroy();
        reject(new Error(`Request timeout after ${requestOptions.timeout}ms`));
      });
      if (options.data && options.method !== "GET") {
        const postData = typeof options.data === "object" ? JSON.stringify(options.data) : String(options.data);
        req.write(postData);
      }
      req.end();
    } catch (error) {
      reject(new Error(`Request error: ${error instanceof Error ? error.message : "Unknown error"}`));
    }
  });
}
const FIGMA_TOKEN = "*********************************************";
async function figmaApiRequest({ url, params, headers }) {
  try {
    const response = await makeHttpRequest(url, {
      params,
      headers: {
        "X-FIGMA-TOKEN": FIGMA_TOKEN,
        ...headers
      }
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
async function parseFigmaUrl(url) {
  try {
    const parsedUrl = new URL(url);
    if (!parsedUrl.hostname.includes("figma.com")) {
      throw new Error("不是有效的 Figma 链接");
    }
    const pathParts = parsedUrl.pathname.split("/").filter((part) => part.length > 0);
    if (pathParts.length < 2 || !["file", "design"].includes(pathParts[0])) {
      throw new Error("无效的 Figma 链接格式");
    }
    const fileKey = pathParts[1];
    let nodeId;
    const nodeIdParam = parsedUrl.searchParams.get("node-id");
    if (nodeIdParam) {
      nodeId = decodeURIComponent(nodeIdParam).replace(":", "-");
    }
    return {
      success: true,
      data: {
        fileKey,
        nodeId,
        originalUrl: url
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
async function getFigmaNodeInfoFromUrl(figmaUrl, nodeId) {
  try {
    const parseResult = await parseFigmaUrl(figmaUrl);
    if (!parseResult.success) {
      throw new Error(`解析Figma URL失败: ${parseResult.error}`);
    }
    const apiUrl = `https://api.figma.com/v1/files/${parseResult.data.fileKey}/nodes`;
    const apiResult = await figmaApiRequest({
      url: apiUrl,
      params: {
        ids: nodeId
      }
    });
    if (!apiResult.success) {
      throw new Error(`获取Figma节点信息失败: ${apiResult.error}`);
    }
    return {
      success: true,
      data: {
        fileKey: parseResult.data.fileKey,
        nodeId,
        figmaUrl,
        figmaInfo: apiResult.data
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
async function downloadImages({ imageUrls, downloadPath }) {
  try {
    if (!fs__namespace.existsSync(downloadPath)) {
      fs__namespace.mkdirSync(downloadPath, { recursive: true });
    }
    const filePaths = [];
    const downloadPromises = [];
    for (const [nodeId, imageUrl] of Object.entries(imageUrls)) {
      if (!imageUrl) continue;
      const downloadPromise = downloadSingleImage(nodeId, imageUrl, downloadPath).then((filePath) => {
        if (filePath) {
          filePaths.push(filePath);
        }
      }).catch((error) => {
        console.error(`下载图片 ${nodeId} 失败:`, error);
      });
      downloadPromises.push(downloadPromise);
    }
    await Promise.all(downloadPromises);
    return {
      success: true,
      data: {
        filePaths
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
async function downloadSingleImage(nodeId, imageUrl, downloadPath) {
  try {
    const response = await makeHttpRequest(imageUrl, {
      responseType: "buffer"
    });
    const fileName = `${nodeId.replace(":", "-")}.png`;
    const filePath = path__namespace.join(downloadPath, fileName);
    fs__namespace.writeFileSync(filePath, response.data);
    console.log(`图片下载成功: ${fileName}`);
    return filePath;
  } catch (error) {
    console.error(`下载图片 ${nodeId} 失败:`, error);
    return null;
  }
}
function setupIpcHandlers() {
  electron.ipcMain.handle("figma-api-request", async (_event, { url, params, headers }) => {
    return await figmaApiRequest({ url, params, headers });
  });
  electron.ipcMain.handle("parse-figma-url", async (_event, url) => {
    return await parseFigmaUrl(url);
  });
  electron.ipcMain.handle("download-images", async (_event, { imageUrls, downloadPath }) => {
    return await downloadImages({ imageUrls, downloadPath });
  });
  electron.ipcMain.handle("show-folder-dialog", async (_event) => {
    try {
      const result = await electron.dialog.showOpenDialog({
        properties: ["openDirectory"],
        title: "选择生成文件夹",
        buttonLabel: "选择文件夹"
      });
      if (result.canceled) {
        return {
          success: false,
          canceled: true
        };
      }
      return {
        success: true,
        data: {
          folderPath: result.filePaths[0]
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "选择文件夹失败"
      };
    }
  });
  electron.ipcMain.handle("validate-and-create-folder", async (_event, folderPath) => {
    try {
      if (fs__namespace.existsSync(folderPath)) {
        const stats = fs__namespace.statSync(folderPath);
        if (stats.isDirectory()) {
          return {
            success: true,
            data: {
              exists: true,
              isDirectory: true,
              message: "文件夹已存在"
            }
          };
        } else {
          return {
            success: false,
            error: "指定路径是一个文件，不是文件夹"
          };
        }
      } else {
        try {
          fs__namespace.mkdirSync(folderPath, { recursive: true });
          return {
            success: true,
            data: {
              exists: false,
              created: true,
              message: "文件夹创建成功"
            }
          };
        } catch (createError) {
          return {
            success: false,
            error: `创建文件夹失败: ${createError instanceof Error ? createError.message : "未知错误"}`
          };
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `验证文件夹失败: ${error instanceof Error ? error.message : "未知错误"}`
      };
    }
  });
  electron.ipcMain.handle("init-project", async (_event, { folderPath, domContent, designWidth }) => {
    return await initProjectFolder(folderPath, domContent, designWidth);
  });
  electron.ipcMain.handle("analyze-html-folder", async (_event, { folderPath, baseFigmaUrl }) => {
    return await analyzeHtmlFolder(folderPath, baseFigmaUrl);
  });
}
async function initProjectFolder(folderPath, domContent, designWidth) {
  try {
    if (!fs__namespace.existsSync(folderPath)) {
      fs__namespace.mkdirSync(folderPath, { recursive: true });
      console.log(`📁 创建文件夹: ${folderPath}`);
    }
    const templatePath = path__namespace.join(process.cwd(), "template.html");
    const targetIndexPath = path__namespace.join(folderPath, "index.html");
    if (!fs__namespace.existsSync(templatePath)) {
      throw new Error("template.html文件不存在于项目根目录");
    }
    let templateContent = fs__namespace.readFileSync(templatePath, "utf-8");
    if (designWidth && designWidth > 0) {
      templateContent = templateContent.replace(/const designWidth = 393;/, `const designWidth = ${designWidth};`);
      console.log(`📐 已将设计稿宽度设置为: ${designWidth}px`);
    }
    if (domContent) {
      templateContent = templateContent.replace("<body>", `<body>
  ${domContent}`);
      console.log("📄 已将DOM结构插入到index.html中");
    }
    fs__namespace.writeFileSync(targetIndexPath, templateContent, "utf-8");
    console.log(`📄 创建index.html: ${targetIndexPath}`);
    const assetsPath = path__namespace.join(folderPath, "assets");
    if (!fs__namespace.existsSync(assetsPath)) {
      fs__namespace.mkdirSync(assetsPath, { recursive: true });
      console.log(`📁 创建assets目录: ${assetsPath}`);
    }
    return {
      success: true,
      data: {
        folderPath,
        indexPath: targetIndexPath,
        assetsPath,
        message: "项目文件夹初始化成功"
      }
    };
  } catch (error) {
    console.error("❌ 初始化项目文件夹失败:", error);
    return {
      success: false,
      error: `初始化项目文件夹失败: ${error instanceof Error ? error.message : "未知错误"}`
    };
  }
}
async function analyzeHtmlFolder(folderPath, baseFigmaUrl) {
  try {
    if (!fs__namespace.existsSync(folderPath)) {
      throw new Error(`文件夹路径不存在: ${folderPath}`);
    }
    const indexHtmlPath = path__namespace.join(folderPath, "index.html");
    if (!fs__namespace.existsSync(indexHtmlPath)) {
      throw new Error(`index.html文件不存在: ${indexHtmlPath}`);
    }
    const htmlContent = fs__namespace.readFileSync(indexHtmlPath, "utf-8");
    const classNames = extractClassNames(htmlContent);
    console.log("🔍 提取到的所有class名称:", classNames);
    const definedClasses = extractDefinedClasses(htmlContent);
    console.log("🎨 style标签中定义的类名:", Array.from(definedClasses));
    const undefinedClasses = classNames.filter((className) => !definedClasses.has(className));
    console.log("❌ 未定义样式的类名:", undefinedClasses);
    if (undefinedClasses.length === 0) {
      console.log("✅ 所有类名都已定义样式");
      return {
        success: true,
        data: {
          message: "所有类名都已定义样式",
          allClasses: classNames,
          definedClasses: Array.from(definedClasses),
          undefinedClasses: []
        }
      };
    }
    const targetClassName = undefinedClasses[0];
    console.log("🎯 选择处理的类名:", targetClassName);
    if (!targetClassName.startsWith("_")) {
      throw new Error(`类名格式不符合要求，应该以下划线开头: ${targetClassName}`);
    }
    const nodeId = targetClassName.substring(1);
    console.log("🔑 提取到的节点ID:", nodeId);
    let figmaUrl;
    if (baseFigmaUrl) {
      figmaUrl = `${baseFigmaUrl}?node-id=${nodeId.replace("-", ":")}&t=0cP6qb0lWwh00jvz-4`;
    } else {
      const defaultBaseFigmaUrl = "https://www.figma.com/design/w7ZjgUryNqGb1UPKXCOoO6/Task-Management--Todo-App--Community-";
      figmaUrl = `${defaultBaseFigmaUrl}?node-id=${nodeId.replace("-", ":")}&t=0cP6qb0lWwh00jvz-4`;
    }
    console.log("🔗 构造的Figma URL:", figmaUrl);
    console.log("📡 正在获取Figma节点信息...");
    const nodeInfoResult = await getFigmaNodeInfoFromUrl(figmaUrl, nodeId);
    if (!nodeInfoResult.success) {
      throw new Error(`获取Figma节点信息失败: ${nodeInfoResult.error}`);
    }
    console.log("✅ 成功获取Figma节点信息");
    return {
      success: true,
      data: {
        targetClassName,
        nodeId,
        figmaUrl,
        figmaInfo: nodeInfoResult.data.figmaInfo,
        allClasses: classNames,
        definedClasses: Array.from(definedClasses),
        undefinedClasses
      }
    };
  } catch (error) {
    console.error("❌ 分析HTML文件夹失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "未知错误"
    };
  }
}
function extractClassNames(htmlContent) {
  const classNames = /* @__PURE__ */ new Set();
  const classRegex = /class\s*=\s*["']([^"']+)["']/gi;
  let match;
  while ((match = classRegex.exec(htmlContent)) !== null) {
    const classValue = match[1];
    const individualClasses = classValue.split(/\s+/).filter((cls) => cls.trim().length > 0);
    individualClasses.forEach((cls) => classNames.add(cls.trim()));
  }
  return Array.from(classNames);
}
function extractDefinedClasses(htmlContent) {
  const definedClasses = /* @__PURE__ */ new Set();
  const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
  let match;
  while ((match = styleRegex.exec(htmlContent)) !== null) {
    const styleContent = match[1];
    const classSelectors = extractCssClassSelectors(styleContent);
    classSelectors.forEach((cls) => definedClasses.add(cls));
  }
  return definedClasses;
}
function extractCssClassSelectors(cssContent) {
  const classNames = /* @__PURE__ */ new Set();
  const classRegex = /\.([a-zA-Z_][a-zA-Z0-9_-]*)/g;
  let match;
  while ((match = classRegex.exec(cssContent)) !== null) {
    classNames.add(match[1]);
  }
  return Array.from(classNames);
}
node_module.createRequire(typeof document === "undefined" ? require("url").pathToFileURL(__filename).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === "SCRIPT" && _documentCurrentScript.src || new URL("main.js", document.baseURI).href);
const __dirname$1 = path$1.dirname(node_url.fileURLToPath(typeof document === "undefined" ? require("url").pathToFileURL(__filename).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === "SCRIPT" && _documentCurrentScript.src || new URL("main.js", document.baseURI).href));
process.env.DIST = path$1.join(__dirname$1, "../dist");
process.env.VITE_PUBLIC = electron.app.isPackaged ? process.env.DIST : path$1.join(process.env.DIST, "../public");
let win;
function createWindow() {
  win = new electron.BrowserWindow({
    width: 1200,
    height: 800,
    icon: path$1.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path$1.join(__dirname$1, "preload.js"),
      nodeIntegration: false,
      contextIsolation: true
    }
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  if (process.env.VITE_DEV_SERVER_URL) {
    win.loadURL(process.env.VITE_DEV_SERVER_URL);
    win.webContents.openDevTools();
  } else {
    win.loadFile(path$1.join(process.env.DIST, "index.html"));
  }
}
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
    win = null;
  }
});
electron.app.on("activate", () => {
  if (electron.BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
electron.app.whenReady().then(() => {
  createWindow();
  setupIpcHandlers();
});
