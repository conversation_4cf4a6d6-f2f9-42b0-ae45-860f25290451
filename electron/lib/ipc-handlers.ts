import { ipcMain, dialog } from 'electron'
import * as fs from 'fs'
import * as path from 'path'
import { figmaApiRequest, parseFigmaUrl, getFigmaNodeInfoFromUrl } from './figma-api'
import { downloadImages } from './image-downloader'

/**
 * 设置所有 IPC 处理器
 */
export function setupIpcHandlers(): void {
  // Figma API 请求处理器 - 只负责纯粹的API请求，不处理数据
  ipcMain.handle('figma-api-request', async (_event, { url, params, headers }) => {
    return await figmaApiRequest({ url, params, headers })
  })

  // Figma 链接解析处理器
  ipcMain.handle('parse-figma-url', async (_event, url: string) => {
    return await parseFigmaUrl(url)
  })

  // 图片下载处理器
  ipcMain.handle('download-images', async (_event, { imageUrls, downloadPath }) => {
    return await downloadImages({ imageUrls, downloadPath })
  })

  // 文件夹选择对话框处理器
  ipcMain.handle('show-folder-dialog', async (_event) => {
    try {
      const result = await dialog.showOpenDialog({
        properties: ['openDirectory'],
        title: '选择生成文件夹',
        buttonLabel: '选择文件夹'
      })

      if (result.canceled) {
        return {
          success: false,
          canceled: true
        }
      }

      return {
        success: true,
        data: {
          folderPath: result.filePaths[0]
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '选择文件夹失败'
      }
    }
  })

  // 验证并创建文件夹处理器
  ipcMain.handle('validate-and-create-folder', async (_event, folderPath: string) => {
    try {
      // 检查路径是否存在
      if (fs.existsSync(folderPath)) {
        // 检查是否是文件夹
        const stats = fs.statSync(folderPath)
        if (stats.isDirectory()) {
          return {
            success: true,
            data: {
              exists: true,
              isDirectory: true,
              message: '文件夹已存在'
            }
          }
        } else {
          return {
            success: false,
            error: '指定路径是一个文件，不是文件夹'
          }
        }
      } else {
        // 文件夹不存在，尝试创建
        try {
          fs.mkdirSync(folderPath, { recursive: true })
          return {
            success: true,
            data: {
              exists: false,
              created: true,
              message: '文件夹创建成功'
            }
          }
        } catch (createError) {
          return {
            success: false,
            error: `创建文件夹失败: ${createError instanceof Error ? createError.message : '未知错误'}`
          }
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `验证文件夹失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  })

  // 初始化项目文件夹处理器
  ipcMain.handle('init-project', async (_event, { folderPath, domContent, designWidth }: { folderPath: string, domContent?: string, designWidth?: number }) => {
    return await initProjectFolder(folderPath, domContent, designWidth)
  })

  // HTML分析处理器
  ipcMain.handle('analyze-html-folder', async (_event, { folderPath, baseFigmaUrl }: { folderPath: string, baseFigmaUrl?: string }) => {
    return await analyzeHtmlFolder(folderPath, baseFigmaUrl)
  })
}

/**
 * 初始化项目文件夹
 * 1. 创建文件夹
 * 2. 复制template.html到新文件夹并重命名为index.html
 * 3. 如果提供了DOM内容，将其插入到index.html中
 * 4. 如果提供了设计稿宽度，替换模板中的宽度值
 * 5. 创建assets子目录
 */
async function initProjectFolder(folderPath: string, domContent?: string, designWidth?: number): Promise<{success: boolean, data?: any, error?: string}> {
  try {
    // 1. 创建主文件夹（如果不存在）
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true })
      console.log(`📁 创建文件夹: ${folderPath}`)
    }

    // 2. 复制template.html到新文件夹并重命名为index.html
    const templatePath = path.join(process.cwd(), 'template.html')
    const targetIndexPath = path.join(folderPath, 'index.html')

    if (!fs.existsSync(templatePath)) {
      throw new Error('template.html文件不存在于项目根目录')
    }

    // 读取模板内容
    let templateContent = fs.readFileSync(templatePath, 'utf-8')

    // 如果提供了设计稿宽度，替换模板中的宽度值
    if (designWidth && designWidth > 0) {
      templateContent = templateContent.replace(/const designWidth = 393;/, `const designWidth = ${designWidth};`)
      console.log(`📐 已将设计稿宽度设置为: ${designWidth}px`)
    }

    // 如果提供了DOM内容，将其插入到body标签中
    if (domContent) {
      templateContent = templateContent.replace('<body>', `<body>\n  ${domContent}`)
      console.log('📄 已将DOM结构插入到index.html中')
    }

    // 写入index.html文件
    fs.writeFileSync(targetIndexPath, templateContent, 'utf-8')
    console.log(`📄 创建index.html: ${targetIndexPath}`)

    // 3. 创建assets子目录
    const assetsPath = path.join(folderPath, 'assets')
    if (!fs.existsSync(assetsPath)) {
      fs.mkdirSync(assetsPath, { recursive: true })
      console.log(`📁 创建assets目录: ${assetsPath}`)
    }

    return {
      success: true,
      data: {
        folderPath,
        indexPath: targetIndexPath,
        assetsPath,
        message: '项目文件夹初始化成功'
      }
    }

  } catch (error) {
    console.error('❌ 初始化项目文件夹失败:', error)
    return {
      success: false,
      error: `初始化项目文件夹失败: ${error instanceof Error ? error.message : '未知错误'}`
    }
  }
}

/**
 * 分析HTML文件夹中的index.html，找到未定义样式的类名并获取Figma节点信息
 * @param folderPath 文件夹路径
 * @param baseFigmaUrl 可选的基础Figma URL
 * @returns Promise<{success: boolean, data?: any, error?: string}>
 */
async function analyzeHtmlFolder(folderPath: string, baseFigmaUrl?: string): Promise<{success: boolean, data?: any, error?: string}> {
  try {
    // 1. 检查文件夹路径是否存在
    if (!fs.existsSync(folderPath)) {
      throw new Error(`文件夹路径不存在: ${folderPath}`);
    }

    // 2. 查找index.html文件
    const indexHtmlPath = path.join(folderPath, 'index.html');
    if (!fs.existsSync(indexHtmlPath)) {
      throw new Error(`index.html文件不存在: ${indexHtmlPath}`);
    }

    // 3. 读取HTML文件内容
    const htmlContent = fs.readFileSync(indexHtmlPath, 'utf-8');

    // 4. 解析HTML内容，提取所有class属性
    const classNames = extractClassNames(htmlContent);
    console.log('🔍 提取到的所有class名称:', classNames);

    // 5. 提取style标签中定义的CSS类名
    const definedClasses = extractDefinedClasses(htmlContent);
    console.log('🎨 style标签中定义的类名:', Array.from(definedClasses));

    // 6. 找到存在于DOM中但不存在于style标签中的类名
    const undefinedClasses = classNames.filter(className => !definedClasses.has(className));
    console.log('❌ 未定义样式的类名:', undefinedClasses);

    if (undefinedClasses.length === 0) {
      console.log('✅ 所有类名都已定义样式');
      return {
        success: true,
        data: {
          message: '所有类名都已定义样式',
          allClasses: classNames,
          definedClasses: Array.from(definedClasses),
          undefinedClasses: []
        }
      };
    }

    // 7. 取第一个未定义的类名进行处理
    const targetClassName = undefinedClasses[0];
    console.log('🎯 选择处理的类名:', targetClassName);

    // 8. 检查类名格式是否符合 "_12:14" 这样的格式
    if (!targetClassName.startsWith('_')) {
      throw new Error(`类名格式不符合要求，应该以下划线开头: ${targetClassName}`);
    }

    // 9. 去除开头的下划线，得到ID
    const nodeId = targetClassName.substring(1);
    console.log('🔑 提取到的节点ID:', nodeId);

    // 10. 构造Figma URL
    let figmaUrl: string;
    if (baseFigmaUrl) {
      figmaUrl = `${baseFigmaUrl}?node-id=${nodeId.replace('-', ':')}&t=0cP6qb0lWwh00jvz-4`;
    } else {
      // 使用默认的Figma URL
      const defaultBaseFigmaUrl = 'https://www.figma.com/design/w7ZjgUryNqGb1UPKXCOoO6/Task-Management--Todo-App--Community-';
      figmaUrl = `${defaultBaseFigmaUrl}?node-id=${nodeId.replace('-', ':')}&t=0cP6qb0lWwh00jvz-4`;
    }

    console.log('🔗 构造的Figma URL:', figmaUrl);

    // 11. 使用getFigmaNodeInfoFromUrl获取节点信息
    console.log('📡 正在获取Figma节点信息...');
    const nodeInfoResult = await getFigmaNodeInfoFromUrl(figmaUrl, nodeId);

    if (!nodeInfoResult.success) {
      throw new Error(`获取Figma节点信息失败: ${nodeInfoResult.error}`);
    }

    console.log('✅ 成功获取Figma节点信息');

    return {
      success: true,
      data: {
        targetClassName,
        nodeId,
        figmaUrl,
        figmaInfo: nodeInfoResult.data.figmaInfo,
        allClasses: classNames,
        definedClasses: Array.from(definedClasses),
        undefinedClasses
      }
    };

  } catch (error) {
    console.error('❌ 分析HTML文件夹失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 从HTML内容中提取所有class属性值
 * @param htmlContent HTML内容
 * @returns string[] 所有class名称的数组
 */
function extractClassNames(htmlContent: string): string[] {
  const classNames = new Set<string>();

  // 使用正则表达式匹配所有class属性
  const classRegex = /class\s*=\s*["']([^"']+)["']/gi;
  let match;

  while ((match = classRegex.exec(htmlContent)) !== null) {
    const classValue = match[1];
    // 分割多个类名（用空格分隔）
    const individualClasses = classValue.split(/\s+/).filter(cls => cls.trim().length > 0);
    individualClasses.forEach(cls => classNames.add(cls.trim()));
  }

  return Array.from(classNames);
}

/**
 * 从HTML内容的style标签中提取已定义的CSS类名
 * @param htmlContent HTML内容
 * @returns Set<string> 已定义的类名集合
 */
function extractDefinedClasses(htmlContent: string): Set<string> {
  const definedClasses = new Set<string>();

  // 提取所有style标签内容
  const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
  let match;

  while ((match = styleRegex.exec(htmlContent)) !== null) {
    const styleContent = match[1];

    // 从CSS内容中提取类选择器
    const classSelectors = extractCssClassSelectors(styleContent);
    classSelectors.forEach(cls => definedClasses.add(cls));
  }

  return definedClasses;
}

/**
 * 从CSS内容中提取类选择器
 * @param cssContent CSS内容
 * @returns string[] 类选择器数组
 */
function extractCssClassSelectors(cssContent: string): string[] {
  const classNames = new Set<string>();

  // 匹配CSS类选择器 .className
  const classRegex = /\.([a-zA-Z_][a-zA-Z0-9_-]*)/g;
  let match;

  while ((match = classRegex.exec(cssContent)) !== null) {
    classNames.add(match[1]);
  }

  return Array.from(classNames);
}

