import { makeHttpRequest } from './http-client'
import type { FigmaNodesResponse, FigmaNodeInfo, FigmaNode } from '../../type/figma'

// Figma API配置
const FIGMA_TOKEN = "*********************************************"

export interface FigmaApiRequestParams {
  url: string
  params?: Record<string, any>
  headers?: Record<string, string>
}

export interface FigmaUrlParseResult {
  fileKey: string
  nodeId?: string
  originalUrl: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

/**
 * 发送 Figma API 请求
 */
export async function figmaApiRequest({ url, params, headers }: FigmaApiRequestParams): Promise<ApiResponse> {
  try {
    const response = await makeHttpRequest(url, {
      params,
      headers: {
        'X-FIGMA-TOKEN': FIGMA_TOKEN,
        ...headers
      }
    })
    return { success: true, data: response.data }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * 解析 Figma 链接，提取文件密钥和节点ID
 */
export async function parseFigmaUrl(url: string): Promise<ApiResponse<FigmaUrlParseResult>> {
  try {
    const parsedUrl = new URL(url)

    if (!parsedUrl.hostname.includes('figma.com')) {
      throw new Error('不是有效的 Figma 链接')
    }

    const pathParts = parsedUrl.pathname.split('/').filter(part => part.length > 0)

    if (pathParts.length < 2 || !['file', 'design'].includes(pathParts[0])) {
      throw new Error('无效的 Figma 链接格式')
    }

    const fileKey = pathParts[1]

    let nodeId: string | undefined
    const nodeIdParam = parsedUrl.searchParams.get('node-id')
    if (nodeIdParam) {
      nodeId = decodeURIComponent(nodeIdParam).replace(':', '-')
    }

    return {
      success: true,
      data: {
        fileKey,
        nodeId,
        originalUrl: url
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * 根据 Figma 链接获取节点信息 (主进程版本)
 * @param figmaUrl Figma 分享链接
 * @param nodeId 节点ID
 * @returns Promise<ApiResponse>
 */
export async function getFigmaNodeInfoFromUrl(figmaUrl: string, nodeId: string): Promise<ApiResponse> {
  try {
    // 解析链接获取文件key
    const parseResult = await parseFigmaUrl(figmaUrl)
    if (!parseResult.success) {
      throw new Error(`解析Figma URL失败: ${parseResult.error}`)
    }

    // 调用Figma API获取节点信息
    const apiUrl = `https://api.figma.com/v1/files/${parseResult.data.fileKey}/nodes`
    const apiResult = await figmaApiRequest({
      url: apiUrl,
      params: {
        ids: nodeId
      }
    })

    if (!apiResult.success) {
      throw new Error(`获取Figma节点信息失败: ${apiResult.error}`)
    }

    return {
      success: true,
      data: {
        fileKey: parseResult.data.fileKey,
        nodeId,
        figmaUrl,
        figmaInfo: apiResult.data
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}


