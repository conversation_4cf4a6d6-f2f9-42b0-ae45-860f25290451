{"version": 3, "file": "terminal.test.js", "sourceRoot": "", "sources": ["../src/terminal.test.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;AAEH,+BAAiC;AACjC,qDAAoD;AACpD,+CAA8C;AAC9C,uCAAsC;AAGtC,IAAM,mBAAmB,GAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,iCAAe,CAAC,CAAC,CAAC,2BAAY,CAAC;AAC5F,IAAM,KAAK,GAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC;AAEvE,IAAI,YAA4C,CAAC;AACjD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;IAChC,YAAY,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;CAC7C;KAAM;IACL,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;CAC1C;AAED;IAA2B,gCAAQ;IAAnC;;IA4BA,CAAC;IA3BQ,gCAAS,GAAhB,UAAoB,IAAY,EAAE,KAAQ,EAAE,IAAY,EAAE,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QACnF,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IACjD,CAAC;IACS,6BAAM,GAAhB,UAAiB,IAAY;QAC3B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IACM,6BAAM,GAAb,UAAc,IAAY,EAAE,IAAY;QACtC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IACM,4BAAK,GAAZ;QACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IACM,8BAAO,GAAd;QACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IACM,2BAAI,GAAX,UAAY,MAAe;QACzB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IACD,sBAAW,iCAAO;aAAlB;YACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;;;OAAA;IACD,sBAAW,gCAAM;aAAjB;YACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;;;OAAA;IACD,sBAAW,+BAAK;aAAhB;YACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;;;OAAA;IACH,mBAAC;AAAD,CAAC,AA5BD,CAA2B,mBAAQ,GA4BlC;AAED,QAAQ,CAAC,UAAU,EAAE;IACnB,QAAQ,CAAC,aAAa,EAAE;QACtB,EAAE,CAAC,6BAA6B,EAAE;YAChC,MAAM,CAAC,MAAM,CACX,cAAM,OAAA,IAAU,YAAa,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,EAAjD,CAAiD,EACvD,sCAAsC,CACvC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE;QACpB,EAAE,CAAC,iCAAiC,EAAE;YACpC,IAAM,CAAC,GAAG,IAAI,YAAY,EAAE,CAAC;YAC7B,MAAM,CAAC,YAAY,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,EAApC,CAAoC,CAAC,CAAC;YAChE,MAAM,CAAC,YAAY,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,EAA/B,CAA+B,CAAC,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAhC,CAAgC,CAAC,CAAC;YAE5D,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,EAApC,CAAoC,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,EAA/B,CAA+B,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAhC,CAAgC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,4CAA4C,EAAE;YAC/C,IAAM,CAAC,GAAG,IAAI,YAAY,EAAE,CAAC;YAC7B,MAAM,CAAC,YAAY,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAA5C,CAA4C,CAAC,CAAC;YACxE,MAAM,CAAC,YAAY,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAvC,CAAuC,CAAC,CAAC;YACnE,MAAM,CAAC,YAAY,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAxC,CAAwC,CAAC,CAAC;YAEpE,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAA5C,CAA4C,CAAC,CAAC;YAClE,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAvC,CAAuC,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAxC,CAAwC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAM,GAAG,GAAG,IAAI,mBAAmB,CAAC,KAAK,EAAE,EAAE,EAAE,EAAC,iBAAiB,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAC,CAAC,CAAC;YAC7H,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAE,GAAW,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAE,GAAW,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QACH,oFAAoF;QACpF,+EAA+E;QAC/E,4CAA4C;QAC5C,wCAAwC;QACxC,cAAc;QACd,MAAM;QAEN,yBAAyB;QACzB,uIAAuI;QACvI,2BAA2B;QAC3B,0CAA0C;QAC1C,6CAA6C;QAC7C,+CAA+C;QAC/C,oBAAoB;QACpB,wBAAwB;QACxB,oBAAoB;QACpB,yBAAyB;QACzB,oBAAoB;QACpB,4BAA4B;QAC5B,sEAAsE;QACtE,iBAAiB;QACjB,MAAM;IACR,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,SAAS,oBAAoB,CAAC,IAAY;IACxC,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AACxC,CAAC"}