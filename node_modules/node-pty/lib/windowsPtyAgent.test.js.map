{"version": 3, "file": "windowsPtyAgent.test.js", "sourceRoot": "", "sources": ["../src/windowsPtyAgent.test.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAEH,+BAAiC;AACjC,qDAAsD;AAEtD,SAAS,KAAK,CAAC,IAAY,EAAE,IAAuB,EAAE,QAAgB;IACpE,MAAM,CAAC,KAAK,CAAC,mCAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;AACxD,CAAC;AAED,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;IAChC,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,QAAQ,CAAC,eAAe,EAAE;YACxB,EAAE,CAAC,6BAA6B,EAAE;gBAChC,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,6BAA6B,EAAE;gBAChC,KAAK,CAAC,gBAAgB,EAAE,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,sCAAsC,EAAE;gBACzC,KAAK,CAAC,cAAc,EAAE,EAAE,EAAE,cAAc,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,gCAAgC,EAAE;gBACnC,KAAK,CAAC,aAAa,EAAE,EAAE,EAAE,mBAAmB,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,mCAAmC,EAAE;gBACtC,KAAK,CAAC,aAAa,EAAE,EAAE,EAAE,iBAAiB,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE;YACzB,EAAE,CAAC,2BAA2B,EAAE;gBAC9B,KAAK,CAAC,WAAW,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,qBAAqB,EAAE;gBACxB,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,yBAAyB,EAAE;gBAC5B,KAAK,CAAC,YAAY,EAAE,EAAE,EAAE,cAAc,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,iCAAiC,EAAE;gBACpC,KAAK,CAAC,iBAAiB,EAAE,EAAE,EAAE,qBAAqB,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,sCAAsC,EAAE;gBACzC,KAAK,CAAC,eAAe,EAAE,EAAE,EAAE,iBAAiB,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,mCAAmC,EAAE;gBACtC,KAAK,CAAC,cAAc,EAAE,EAAE,EAAE,oBAAoB,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,yCAAyC,EAAE;gBAC5C,KAAK,CAAC,eAAe,EAAE,EAAE,EAAE,qBAAqB,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE;YAC7B,EAAE,CAAC,6BAA6B,EAAE;gBAChC,KAAK,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,yBAAyB,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,8BAA8B,EAAE;gBACjC,KAAK,CAAC,MAAM,EAAE,CAAC,uBAAuB,CAAC,EAAE,gCAAgC,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,qCAAqC,EAAE;gBACxC,KAAK,CAAC,MAAM,EAAE,CAAC,wBAAwB,CAAC,EAAE,mCAAmC,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,kCAAkC,EAAE;gBACrC,KAAK,CAAC,MAAM,EAAE,CAAC,qBAAqB,CAAC,EAAE,gCAAgC,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,gCAAgC,EAAE;gBACnC,KAAK,CAAC,MAAM,EAAE,CAAC,mBAAmB,CAAC,EAAE,8BAA8B,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qBAAqB,EAAE;YAC9B,EAAE,CAAC,4BAA4B,EAAE;gBAC/B,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,wBAAwB,EAAE;gBAC3B,KAAK,CAAC,MAAM,EAAE,aAAa,EAAE,kBAAkB,CAAC,CAAC;gBACjD,KAAK,CAAC,MAAM,EAAE,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,kBAAkB,EAAE;YAC3B,EAAE,CAAC,sBAAsB,EAAE;gBACzB,KAAK,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,2DAA2D,CAAC,EAAE,2EAA2E,CAAC,CAAC;YACrK,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,qBAAqB,EAAE;gBACxB,KAAK,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,8CAA8C,CAAC,EAAE,6DAA6D,CAAC,CAAC;YAC1I,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;CACJ"}