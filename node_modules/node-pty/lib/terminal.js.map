{"version": 3, "file": "terminal.js", "sourceRoot": "", "sources": ["../src/terminal.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAGH,iCAAsC;AAEtC,iDAAwD;AAG3C,QAAA,YAAY,GAAW,EAAE,CAAC;AAC1B,QAAA,YAAY,GAAW,EAAE,CAAC;AAEvC;;;;GAIG;AACH,IAAM,kBAAkB,GAAI,MAAM,CAAC,CAAG,mBAAmB;AACzD,IAAM,mBAAmB,GAAG,MAAM,CAAC,CAAG,kBAAkB;AAExD;IA4BE,kBAAY,GAAqB;QA1BvB,SAAI,GAAW,CAAC,CAAC;QACjB,QAAG,GAAW,CAAC,CAAC;QAKhB,UAAK,GAAW,CAAC,CAAC;QAClB,UAAK,GAAW,CAAC,CAAC;QAElB,cAAS,GAAY,KAAK,CAAC;QAC3B,cAAS,GAAY,KAAK,CAAC;QAO7B,YAAO,GAAG,IAAI,6BAAa,EAAU,CAAC;QAEtC,YAAO,GAAG,IAAI,6BAAa,EAAc,CAAC;QAQhD,cAAc;QACd,IAAI,CAAC,WAAW,GAAG,IAAI,qBAAY,EAAE,CAAC;QAEtC,8BAA8B;QAC9B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,iBAAiB,CAAC,CAAC;QACpD,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,gBAAgB,KAAI,kBAAkB,CAAC;QACrE,IAAI,CAAC,kBAAkB,GAAG,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,iBAAiB,KAAI,mBAAmB,CAAC;QAExE,IAAI,CAAC,GAAG,EAAE;YACR,OAAO;SACR;QAED,2FAA2F;QAC3F,+EAA+E;QAC/E,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACnE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACnE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACnE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACjF,CAAC;IA/BD,sBAAW,4BAAM;aAAjB,cAAsC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAElE,sBAAW,4BAAM;aAAjB,cAA0C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAEtE,sBAAW,yBAAG;aAAd,cAA2B,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAC9C,sBAAW,0BAAI;aAAf,cAA4B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAChD,sBAAW,0BAAI;aAAf,cAA4B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IA6BzC,wBAAK,GAAZ,UAAa,IAAY;QACvB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,qDAAqD;YACrD,IAAI,IAAI,KAAK,IAAI,CAAC,iBAAiB,EAAE;gBACnC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,OAAO;aACR;YACD,IAAI,IAAI,KAAK,IAAI,CAAC,kBAAkB,EAAE;gBACpC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,OAAO;aACR;SACF;QACD,uCAAuC;QACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAES,iCAAc,GAAxB;QAAA,iBAGC;QAFC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAApB,CAAoB,CAAC,CAAC;QAC3C,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,QAAQ,EAAE,MAAM,IAAK,OAAA,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,UAAA,EAAE,MAAM,QAAA,EAAE,CAAC,EAAvC,CAAuC,CAAC,CAAC;IACjF,CAAC;IAES,6BAAU,GAApB,UAAwB,IAAY,EAAE,KAAoB,EAAE,IAAY,EAAE,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QACnG,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,OAAO;SACR;QACD,IAAI,UAAU,EAAE;YACd,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,KAAK,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;oBACjB,IAAI,OAAO,CAAC,KAAK,IAAI,EAAE;wBACrB,MAAM,IAAI,KAAK,CAAI,IAAI,SAAI,CAAC,oBAAe,IAAI,gBAAW,OAAO,CAAC,CAAC,CAAC,CAAC,MAAG,CAAC,CAAC;qBAC3E;gBACH,CAAC,CAAC,CAAC;gBACH,OAAO;aACR;SACF;QACD,IAAI,OAAO,KAAK,KAAK,IAAI,EAAE;YACzB,MAAM,IAAI,KAAK,CAAI,IAAI,mBAAc,IAAI,gBAAW,OAAO,KAAK,MAAG,CAAC,CAAC;SACtE;IACH,CAAC;IAED,yBAAyB;IAClB,sBAAG,GAAV,UAAW,IAAY;QACrB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED,+BAA+B;IACxB,uBAAI,GAAX,UAAY,IAAS,EAAE,OAAY;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED,2BAA2B;IACpB,wBAAK,GAAZ;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IAED,4BAA4B;IACrB,yBAAM,GAAb;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;IAC/B,CAAC;IAED,iCAAiC;IAC1B,8BAAW,GAAlB,UAAmB,QAAuB;QACxC,IAAK,IAAI,CAAC,OAAe,CAAC,QAAQ,EAAE;YAClC,OAAQ,IAAI,CAAC,OAAe,CAAC,QAAQ,CAAC;SACvC;QACD,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SACpC;IACH,CAAC;IAEM,8BAAW,GAAlB,UAAmB,SAAiB,EAAE,QAAiC,IAAU,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzG,qBAAE,GAAT,UAAU,SAAiB,EAAE,QAAiC;QAC5D,IAAI,SAAS,KAAK,OAAO,EAAE;YACzB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACvC,OAAO;SACR;QACD,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACvC,CAAC;IAEM,uBAAI,GAAX,UAAY,SAAiB;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAC3C,IAAI,SAAS,KAAK,OAAO,EAAE;YACzB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,SAAgB,CAAC,CAAC;SACxE;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,SAAgB,CAAC,CAAC;IACjE,CAAC;IAEM,4BAAS,GAAhB,UAAiB,SAAiB;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAEM,iCAAc,GAArB,UAAsB,SAAiB,EAAE,QAAiC;QACxE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC;IAEM,qCAAkB,GAAzB,UAA0B,SAAiB;QACzC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC;IAEM,uBAAI,GAAX,UAAY,SAAiB,EAAE,QAAiC;QAC9D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAWS,yBAAM,GAAhB;QACE,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,cAAO,CAAC,CAAC;QACtB,IAAI,CAAC,GAAG,GAAG,cAAO,CAAC,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IACzB,CAAC;IAES,4BAAS,GAAnB,UAAoB,GAAgB;QAClC,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;QACpC,IAAM,KAAK,GAAG,EAAE,CAAC;QAEjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBACzB,SAAS;aACV;YACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1C;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IACH,eAAC;AAAD,CAAC,AA3LD,IA2LC;AA3LqB,4BAAQ"}