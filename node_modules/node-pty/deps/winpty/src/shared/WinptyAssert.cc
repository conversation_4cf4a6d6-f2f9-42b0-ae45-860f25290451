// Copyright (c) 2011-2016 <PERSON>
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to
// deal in the Software without restriction, including without limitation the
// rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
// sell copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
// IN THE SOFTWARE.

#include "WinptyAssert.h"

#include <windows.h>
#include <stdlib.h>

#include "DebugClient.h"

void assertTrace(const char *file, int line, const char *cond) {
    trace("Assertion failed: %s, file %s, line %d",
          cond, file, line);
}

#ifdef WINPTY_AGENT_ASSERT

void agentShutdown() {
    HWND hwnd = GetConsoleWindow();
    if (hwnd != NULL) {
        PostMessage(hwnd, WM_CLOSE, 0, 0);
        Sleep(30000);
        trace("Agent shutdown: WM_CLOSE did not end agent process");
    } else {
        trace("Agent shutdown: GetConsoleWindow() is NULL");
    }
    // abort() prints a message to the console, and if it is frozen, then the
    // process would hang, so instead use exit().  (We shouldn't ever get here,
    // though, because the WM_CLOSE message should have ended this process.)
    exit(1);
}

void agentAssertFail(const char *file, int line, const char *cond) {
    assertTrace(file, line, cond);
    agentShutdown();
}

#endif
